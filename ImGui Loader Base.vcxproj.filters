﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="imgui\imgui.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_demo.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_draw.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_dx9.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_impl_win32.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="imgui\imgui_widgets.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="main.cpp" />
    <ClCompile Include="imgui\imgui_tables.cpp" />
    <ClCompile Include="ui\ui.cc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="imgui\imconfig.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_dx9.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_impl_win32.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imgui_internal.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_rectpack.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_textedit.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="imgui\imstb_truetype.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="main.h" />
    <ClInclude Include="ui\ui.hh" />
    <ClInclude Include="globals.hh" />
    <ClInclude Include="auth\auth.hpp" />
    <ClInclude Include="auth\skStr.h" />
    <ClInclude Include="auth\utils.hpp" />
    <ClInclude Include="auth\json.hpp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="imgui">
      <UniqueIdentifier>{8cf77124-bebc-471d-acac-8b9b1ffc2a66}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>