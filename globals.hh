#pragma once
#include <string>

class c_globals {
public:
	bool active = true;
	char user_name[255] = "";
	char pass_word[255] = "";

	// Login status
	bool login_in_progress = false;
	bool loading_complete = false;
	float loading_timer = 0.0f;
	bool login_attempted = false;
	bool login_successful = false;
	std::string login_message = "";

	// Loading animations
	float loading_rotation = 0.0f;
	float loading_fade_in = 0.0f;
	float loading_scale = 0.0f;

	// UI state
	bool show_password = false;
	bool show_signin = false;
	bool logged_in = false;
	float login_start_time = 0.0f;

	// Input field animations
	bool username_focused = false;
	bool password_focused = false;
	float user_focus_animation = 0.0f;
	float pass_focus_animation = 0.0f;

	// UI transition animations
	float ui_fade_in = 0.0f;
	float form_slide_in = 0.0f;
	float button_hover_animation = 0.0f;
	float success_animation = 0.0f;
};

inline c_globals globals;
